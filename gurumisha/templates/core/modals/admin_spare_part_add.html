<!-- Add New Spare Part Modal -->
<div class="fixed inset-0 z-[1500] overflow-y-auto"
     id="add-spare-part-modal"
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100">

    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-[1400]"
         onclick="if(event.target === this) this.closest('.fixed').remove()"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="modal-panel bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            
            <!-- Modal Header -->
            <div class="modal-header bg-gradient-to-r from-harrier-red to-harrier-dark px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-plus text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">Add New Spare Part</h3>
                            <p class="text-white text-opacity-80 text-sm font-raleway">Add a new spare part to your inventory</p>
                        </div>
                    </div>

                    <!-- Close Button -->
                    <button type="button"
                            class="text-white text-opacity-70 hover:text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-all duration-200"
                            onclick="this.closest('.fixed').remove()">
                        <span class="sr-only">Close</span>
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="add-spare-part-form"
                      method="post"
                      action="{% url 'core:admin_spare_part_add' %}"
                      enctype="multipart/form-data"
                      hx-post="{% url 'core:admin_spare_part_add' %}"
                      hx-on::before-request="debugAdminForm()"
                      hx-on::after-request="handleAdminFormResponse(event)"
                      class="space-y-6"
                      novalidate>
                    {% csrf_token %}

                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle mr-2"></i>Basic Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag"></i>Part Name *
                                </label>
                                <input type="text" name="name" required
                                       class="form-input"
                                       placeholder="Enter part name">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-hashtag"></i>Part Number
                                </label>
                                <input type="text" name="part_number"
                                       class="form-input"
                                       placeholder="Manufacturer part number">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-barcode"></i>SKU *
                                </label>
                                <input type="text" name="sku" required
                                       class="form-input"
                                       placeholder="Stock Keeping Unit">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-qrcode"></i>Barcode
                                </label>
                                <input type="text" name="barcode"
                                       class="form-input"
                                       placeholder="Barcode (optional)">
                            </div>
                        </div>
                    </div>

                    <!-- Category and Classification -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-tags mr-2"></i>Category & Classification
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-folder"></i>Primary Category *
                                </label>
                                <select name="category_new" required class="form-select" onchange="loadSubCategories(this.value)">
                                    <option value="">Select Primary Category</option>
                                    {% for category in categories %}
                                        {% if not category.parent %}
                                            <option value="{{ category.id }}">{{ category.name }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-folder-open"></i>Sub-Category
                                </label>
                                <select name="sub_category" class="form-select" id="sub-category-select">
                                    <option value="">Select Sub-Category (Optional)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-star"></i>Condition *
                                </label>
                                <select name="condition" required class="form-select">
                                    <option value="">Select Condition</option>
                                    <option value="new">New</option>
                                    <option value="used">Used</option>
                                    <option value="refurbished">Refurbished</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-truck"></i>Supplier
                                </label>
                                <select name="supplier" class="form-select">
                                    <option value="">Select Supplier</option>
                                    {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-dollar-sign mr-2"></i>Pricing Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-money-bill"></i>Selling Price (KSh) *
                                </label>
                                <input type="number" name="price" required step="0.01" min="0"
                                       class="form-input"
                                       placeholder="0.00">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-receipt"></i>Cost Price (KSh)
                                </label>
                                <input type="number" name="cost_price" step="0.01" min="0"
                                       class="form-input"
                                       placeholder="0.00">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-percentage"></i>Discount Price (KSh)
                                </label>
                                <input type="number" name="discount_price" step="0.01" min="0"
                                       class="form-input"
                                       placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <!-- Stock Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-boxes mr-2"></i>Stock Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-box"></i>Stock Quantity *
                                </label>
                                <input type="number" name="stock_quantity" required min="0"
                                       class="form-input"
                                       placeholder="0">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-exclamation-triangle"></i>Minimum Stock
                                </label>
                                <input type="number" name="minimum_stock" min="0"
                                       class="form-input"
                                       placeholder="5">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-arrow-up"></i>Maximum Stock
                                </label>
                                <input type="number" name="maximum_stock" min="0"
                                       class="form-input"
                                       placeholder="100">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-redo"></i>Reorder Point *
                                </label>
                                <input type="number" name="reorder_point" min="0" required
                                       class="form-input"
                                       placeholder="10">
                            </div>
                        </div>

                        <!-- Additional Required Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-cube"></i>Unit *
                                </label>
                                <select name="unit" required class="form-input">
                                    <option value="piece" selected>Piece</option>
                                    <option value="set">Set</option>
                                    <option value="pair">Pair</option>
                                    <option value="kit">Kit</option>
                                    <option value="liter">Liter</option>
                                    <option value="meter">Meter</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-shopping-cart"></i>Reorder Quantity *
                                </label>
                                <input type="number" name="reorder_quantity" min="1" required
                                       class="form-input"
                                       placeholder="20" value="20">
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-align-left mr-2"></i>Description & Specifications
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-file-text"></i>Description *
                                </label>
                                <textarea name="description" rows="4" required
                                          class="form-textarea"
                                          placeholder="Detailed description of the spare part"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-cogs"></i>Specifications
                                </label>
                                <textarea name="specifications" rows="4"
                                          class="form-textarea"
                                          placeholder="Technical specifications"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-6 pt-6 border-t border-gray-200">
                        <!-- Cancel Button -->
                        <button type="button"
                                class="enhanced-btn enhanced-btn-cancel"
                                onclick="this.closest('.fixed').remove()">
                            <i class="fas fa-times mr-2"></i>
                            <span>Cancel</span>
                        </button>

                        <!-- Submit Button -->
                        <button type="submit" id="admin-submit-button"
                                class="enhanced-btn enhanced-btn-submit">
                            <i class="fas fa-plus mr-2"></i>
                            <span>Add Spare Part</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Form Section Styling */
    .form-section {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid rgba(229, 231, 235, 0.5);
        backdrop-filter: blur(10px);
    }

    .section-title {
        font-size: 1rem;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        font-family: 'Montserrat', sans-serif;
        border-bottom: 2px solid #F3F4F6;
        padding-bottom: 0.5rem;
    }

    .section-title i {
        color: var(--harrier-red);
        margin-right: 0.5rem;
    }

    /* Enhanced Form Styling */
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-label {
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        font-family: 'Montserrat', sans-serif;
        letter-spacing: 0.025em;
    }

    .form-label i {
        margin-right: 0.5rem;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #6B7280;
    }

    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #E5E7EB;
        border-radius: 8px;
        font-size: 0.875rem;
        font-family: 'Raleway', sans-serif;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
        outline: none;
        border-color: var(--harrier-red);
        box-shadow:
            0 0 0 3px rgba(220, 38, 38, 0.1),
            0 4px 12px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
    }

    .form-input:hover, .form-select:hover, .form-textarea:hover {
        border-color: #9CA3AF;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .form-select {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* Cancel Button Styles */
    .enhanced-btn-cancel {
        background: rgba(255, 255, 255, 0.9);
        color: #6B7280;
        border-color: #D1D5DB;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .enhanced-btn-cancel:hover {
        background: rgba(255, 255, 255, 1);
        color: #374151;
        border-color: #9CA3AF;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Submit Button Styles */
    .enhanced-btn-submit {
        background: linear-gradient(135deg,
            var(--harrier-red) 0%,
            var(--harrier-dark) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(31, 41, 55, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        transform: translateY(-2px);
        box-shadow:
            0 12px 32px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(31, 41, 55, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }

    .enhanced-btn-submit:disabled {
        opacity: 0.8;
        cursor: not-allowed;
        transform: none !important;
        box-shadow:
            0 4px 12px rgba(220, 38, 38, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        background-position: 0% 50% !important;
    }

    /* Mobile Responsiveness */
    @media (max-width: 640px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .form-section {
            padding: 1rem;
        }

        .grid {
            grid-template-columns: 1fr !important;
        }

        .enhanced-btn {
            min-width: 140px;
            padding: 14px 20px;
        }
    }
</style>

<script>
// Debug function to check form data before submission
function debugAdminForm() {
    console.log('=== ADMIN FORM DEBUG ===');
    const form = document.getElementById('add-spare-part-form');
    if (form) {
        const formData = new FormData(form);
        console.log('Form data entries:');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }

        // Check specific fields
        const unitField = form.querySelector('select[name="unit"]');
        const reorderQtyField = form.querySelector('input[name="reorder_quantity"]');

        console.log('Unit field:', unitField);
        console.log('Unit field value:', unitField ? unitField.value : 'FIELD NOT FOUND');
        console.log('Reorder quantity field:', reorderQtyField);
        console.log('Reorder quantity field value:', reorderQtyField ? reorderQtyField.value : 'FIELD NOT FOUND');
    }
    console.log('=== END DEBUG ===');
    return true;
}

// Admin form response handler
function handleAdminFormResponse(event) {
    console.log('Admin form response handler called', event);
    const xhr = event.detail.xhr;
    const response = event.detail.xhr.response;

    console.log('XHR status:', xhr.status);
    console.log('Response:', response);

    // Check if this is a redirect (successful form submission)
    if (xhr.status === 302 || xhr.status === 200) {
        try {
            const data = JSON.parse(response);
            console.log('Parsed JSON data:', data);

            if (data.success) {
                // Handle HTMX JSON success response
                showToast(data.message || 'Spare part added successfully!', 'success');

                // Refresh the admin spare parts table
                refreshAdminSparePartsTable();

                // Close modal after 2 seconds
                setTimeout(() => {
                    const modal = document.querySelector('.fixed');
                    if (modal) modal.remove();
                }, 2000);
            } else if (data.errors) {
                // Handle validation errors
                showToast('Please correct the form errors.', 'error');
                console.error('Form errors:', data.errors);
            }
        } catch (e) {
            // Not JSON response, likely a redirect - this means success
            console.log('Admin form submitted successfully (redirect response)');
            showToast('Spare part added successfully!', 'success');

            // Refresh the admin spare parts table
            refreshAdminSparePartsTable();

            // Close modal after 1.5 seconds
            setTimeout(() => {
                const modal = document.querySelector('.fixed');
                if (modal) modal.remove();
            }, 1500);
        }
    } else {
        // Handle error responses
        showToast(`Server error (${xhr.status}): Please try again later.`, 'error');
    }
}

function refreshAdminSparePartsTable() {
    // Try to refresh the admin spare parts table
    const tableContainer = document.getElementById('inventory-table-container');
    if (tableContainer && typeof htmx !== 'undefined') {
        // Trigger HTMX refresh of the table
        htmx.ajax('GET', window.location.pathname, {
            target: '#inventory-table-container',
            swap: 'outerHTML'
        });
    } else {
        // Fallback: reload the page
        window.location.reload();
    }
}

// Toast notification function (fallback if not available)
function showToast(message, type) {
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // Simple fallback alert
        alert(message);
    }
}

// Load sub-categories based on selected primary category
function loadSubCategories(categoryId) {
    const subCategorySelect = document.getElementById('sub-category-select');

    // Clear existing options
    subCategorySelect.innerHTML = '<option value="">Select Sub-Category (Optional)</option>';

    if (!categoryId) {
        return;
    }

    // Fetch sub-categories via AJAX
    fetch(`/dashboard/admin/spare-parts/subcategories/${categoryId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.subcategories.forEach(subcat => {
                    const option = document.createElement('option');
                    option.value = subcat.id;
                    option.textContent = subcat.name;
                    subCategorySelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading sub-categories:', error);
        });
}
</script>
